import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
export const ConfigurationManager = ({ themeManager, isOpen, onClose, onSave, }) => {
    const [selectedSectionIndex, setSelectedSectionIndex] = useState(0);
    const [selectedSettingIndex, setSelectedSettingIndex] = useState(0);
    const [editingValue, setEditingValue] = useState('');
    const [, setIsEditing] = useState(false);
    const [hasChanges, setHasChanges] = useState(false);
    const [mode, setMode] = useState('sections');
    const [configSections, setConfigSections] = useState([
        {
            id: 'general',
            name: 'General Settings',
            description: 'Basic application configuration',
            settings: [
                {
                    id: 'theme',
                    name: 'Theme',
                    description: 'Application color theme',
                    type: 'select',
                    value: 'default',
                    defaultValue: 'default',
                    options: ['default', 'dark', 'light', 'neon', 'minimal', 'retro'],
                    required: true,
                },
                {
                    id: 'autoSave',
                    name: 'Auto Save',
                    description: 'Automatically save conversations',
                    type: 'boolean',
                    value: true,
                    defaultValue: true,
                },
                {
                    id: 'maxMessages',
                    name: 'Max Messages',
                    description: 'Maximum messages to keep in memory',
                    type: 'number',
                    value: 100,
                    defaultValue: 100,
                    validation: (value) => {
                        const num = Number(value);
                        if (isNaN(num) || num < 1 || num > 1000) {
                            return 'Must be a number between 1 and 1000';
                        }
                        return null;
                    },
                },
            ],
        },
        {
            id: 'providers',
            name: 'AI Providers',
            description: 'Configure AI service providers',
            settings: [
                {
                    id: 'defaultProvider',
                    name: 'Default Provider',
                    description: 'Default AI provider to use',
                    type: 'select',
                    value: 'openai',
                    defaultValue: 'openai',
                    options: ['openai', 'anthropic', 'google', 'deepseek'],
                    required: true,
                },
                {
                    id: 'openaiApiKey',
                    name: 'OpenAI API Key',
                    description: 'API key for OpenAI services',
                    type: 'password',
                    value: '',
                    defaultValue: '',
                },
                {
                    id: 'anthropicApiKey',
                    name: 'Anthropic API Key',
                    description: 'API key for Anthropic services',
                    type: 'password',
                    value: '',
                    defaultValue: '',
                },
                {
                    id: 'googleApiKey',
                    name: 'Google API Key',
                    description: 'API key for Google AI services',
                    type: 'password',
                    value: '',
                    defaultValue: '',
                },
            ],
        },
        {
            id: 'interface',
            name: 'Interface',
            description: 'User interface preferences',
            settings: [
                {
                    id: 'showTimestamps',
                    name: 'Show Timestamps',
                    description: 'Display message timestamps',
                    type: 'boolean',
                    value: true,
                    defaultValue: true,
                },
                {
                    id: 'messageFormat',
                    name: 'Message Format',
                    description: 'How to display messages',
                    type: 'select',
                    value: 'markdown',
                    defaultValue: 'markdown',
                    options: ['markdown', 'plain', 'code'],
                },
                {
                    id: 'terminalWidth',
                    name: 'Terminal Width',
                    description: 'Maximum width for text display',
                    type: 'number',
                    value: 80,
                    defaultValue: 80,
                    validation: (value) => {
                        const num = Number(value);
                        if (isNaN(num) || num < 40 || num > 200) {
                            return 'Must be a number between 40 and 200';
                        }
                        return null;
                    },
                },
            ],
        },
    ]);
    useInput((input, key) => {
        if (!isOpen)
            return;
        if (key.escape) {
            if (mode === 'editing') {
                setMode('settings');
                setIsEditing(false);
                setEditingValue('');
                return;
            }
            if (mode === 'settings') {
                setMode('sections');
                return;
            }
            if (hasChanges) {
                // Could show confirmation dialog here
            }
            onClose();
            return;
        }
        if (mode === 'sections') {
            if (key.upArrow && selectedSectionIndex > 0) {
                setSelectedSectionIndex(selectedSectionIndex - 1);
                return;
            }
            if (key.downArrow && selectedSectionIndex < configSections.length - 1) {
                setSelectedSectionIndex(selectedSectionIndex + 1);
                return;
            }
            if (key.return) {
                setMode('settings');
                setSelectedSettingIndex(0);
                return;
            }
        }
        if (mode === 'settings') {
            const currentSection = configSections[selectedSectionIndex];
            if (key.upArrow && selectedSettingIndex > 0) {
                setSelectedSettingIndex(selectedSettingIndex - 1);
                return;
            }
            if (key.downArrow && selectedSettingIndex < currentSection.settings.length - 1) {
                setSelectedSettingIndex(selectedSettingIndex + 1);
                return;
            }
            if (key.return) {
                const setting = currentSection.settings[selectedSettingIndex];
                if (setting.type === 'boolean') {
                    updateSettingValue(setting.id, !setting.value);
                }
                else {
                    setMode('editing');
                    setIsEditing(true);
                    setEditingValue(setting.value?.toString() || '');
                }
                return;
            }
        }
        if (mode === 'editing') {
            if (key.return) {
                const setting = configSections[selectedSectionIndex].settings[selectedSettingIndex];
                const validation = setting.validation?.(editingValue);
                if (!validation) {
                    let value = editingValue;
                    if (setting.type === 'number') {
                        value = Number(editingValue);
                    }
                    else if (setting.type === 'boolean') {
                        value = editingValue.toLowerCase() === 'true';
                    }
                    updateSettingValue(setting.id, value);
                    setMode('settings');
                    setIsEditing(false);
                    setEditingValue('');
                }
                return;
            }
            if (key.backspace || key.delete) {
                setEditingValue(prev => prev.slice(0, -1));
                return;
            }
            if (input && input.length === 1 && !key.ctrl && !key.meta) {
                setEditingValue(prev => prev + input);
            }
        }
        // Global shortcuts
        if (key.ctrl && input === 's') {
            handleSave();
            return;
        }
    });
    const updateSettingValue = (settingId, value) => {
        setConfigSections(prev => prev.map(section => ({
            ...section,
            settings: section.settings.map(setting => setting.id === settingId ? { ...setting, value } : setting),
        })));
        setHasChanges(true);
    };
    const handleSave = () => {
        const config = {};
        configSections.forEach(section => {
            section.settings.forEach(setting => {
                config[setting.id] = setting.value;
            });
        });
        onSave?.(config);
        setHasChanges(false);
    };
    if (!isOpen)
        return null;
    const renderSectionsList = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('Configuration Sections:') }), _jsx(Text, {}), configSections.map((section, index) => (_jsxs(Box, { marginLeft: 2, children: [_jsxs(Text, { children: [selectedSectionIndex === index ? themeManager.highlight(' ▶ ') : '   ', themeManager.primary(section.name)] }), _jsxs(Text, { children: [" - ", themeManager.muted(section.description)] })] }, section.id)))] }));
    const renderSettingsList = () => {
        const currentSection = configSections[selectedSectionIndex];
        return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary(`${currentSection.name} Settings:`) }), _jsx(Text, {}), currentSection.settings.map((setting, index) => (_jsx(Box, { marginLeft: 2, marginBottom: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Box, { children: _jsxs(Text, { children: [selectedSettingIndex === index ? themeManager.highlight(' ▶ ') : '   ', themeManager.primary(setting.name), setting.required && _jsx(Text, { children: themeManager.error(' *') })] }) }), _jsx(Box, { marginLeft: 3, children: _jsx(Text, { children: themeManager.muted(setting.description) }) }), _jsxs(Box, { marginLeft: 3, children: [_jsx(Text, { children: themeManager.secondary('Value: ') }), setting.type === 'password' ? (_jsx(Text, { children: themeManager.accent('*'.repeat(setting.value?.length || 0)) })) : (_jsx(Text, { children: themeManager.accent(setting.value?.toString() || 'Not set') }))] })] }) }, setting.id)))] }));
    };
    const renderEditingInterface = () => {
        const setting = configSections[selectedSectionIndex].settings[selectedSettingIndex];
        const validation = setting.validation?.(editingValue);
        return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary(`Editing: ${setting.name}`) }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('Description: ') }), _jsx(Text, { children: themeManager.muted(setting.description) })] }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('Current Value: ') }), _jsx(Text, { children: themeManager.primary(editingValue || '...') })] }), setting.options && (_jsxs(Box, { marginLeft: 2, marginTop: 1, children: [_jsx(Text, { children: themeManager.secondary('Options: ') }), _jsx(Text, { children: themeManager.muted(setting.options.join(', ')) })] })), validation && (_jsx(Box, { marginLeft: 2, marginTop: 1, children: _jsx(Text, { children: themeManager.error(`Error: ${validation}`) }) }))] }));
    };
    const renderControls = () => (_jsx(Box, { marginTop: 1, borderStyle: "single", borderColor: "gray", padding: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.muted('Controls:') }), mode === 'sections' && (_jsx(Text, { children: themeManager.muted('  ↑↓ Navigate • Enter Select • Esc Close') })), mode === 'settings' && (_jsx(Text, { children: themeManager.muted('  ↑↓ Navigate • Enter Edit • Esc Back • Ctrl+S Save') })), mode === 'editing' && (_jsx(Text, { children: themeManager.muted('  Type value • Enter Save • Esc Cancel') })), hasChanges && (_jsx(Text, { children: themeManager.warning('  Unsaved changes!') }))] }) }));
    return (_jsx(Box, { flexDirection: "column", padding: 1, children: _jsx(Box, { borderStyle: "round", borderColor: "blue", padding: 1, children: _jsxs(Box, { flexDirection: "column", width: "100%", children: [_jsx(Text, { children: themeManager.primary('⚙️  Configuration Manager') }), _jsx(Text, {}), mode === 'sections' && renderSectionsList(), mode === 'settings' && renderSettingsList(), mode === 'editing' && renderEditingInterface(), renderControls()] }) }) }));
};
//# sourceMappingURL=ConfigurationManager.js.map