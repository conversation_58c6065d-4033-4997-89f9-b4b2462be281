/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { LoadingIndicator } from './LoadingIndicator.js';

interface LoadingScreenProps {
  themeManager: ThemeManager;
  status?: string;
  progress?: number;
  showBranding?: boolean;
  showProgress?: boolean;
  showStatus?: boolean;
  onComplete?: () => void;
}

interface LoadingStep {
  id: string;
  label: string;
  status: 'pending' | 'loading' | 'complete' | 'error';
  duration?: number;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  themeManager,
  status = 'Initializing Arien AI CLI...',
  showBranding = true,
  showProgress = true,
  showStatus = true,
  onComplete,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loadingSteps, setLoadingSteps] = useState<LoadingStep[]>([
    { id: 'init', label: 'Initializing application', status: 'loading' },
    { id: 'config', label: 'Loading configuration', status: 'pending' },
    { id: 'providers', label: 'Setting up AI providers', status: 'pending' },
    { id: 'tools', label: 'Discovering tools', status: 'pending' },
    { id: 'themes', label: 'Loading themes', status: 'pending' },
    { id: 'ready', label: 'Ready to start', status: 'pending' },
  ]);

  // Simulate loading progress
  useEffect(() => {
    const interval = setInterval(() => {
      setLoadingSteps(prev => {
        const newSteps = [...prev];
        const current = newSteps[currentStep];
        
        if (current && current.status === 'loading') {
          // Complete current step
          current.status = 'complete';
          
          // Start next step
          if (currentStep < newSteps.length - 1) {
            newSteps[currentStep + 1].status = 'loading';
            setCurrentStep(currentStep + 1);
          } else {
            // All steps complete
            setTimeout(() => {
              onComplete?.();
            }, 500);
          }
        }
        
        return newSteps;
      });
    }, 800 + Math.random() * 400); // Random delay between 800-1200ms

    return () => clearInterval(interval);
  }, [currentStep, onComplete]);

  const renderBranding = () => {
    if (!showBranding) return null;

    return (
      <Box flexDirection="column" alignItems="center" marginBottom={2}>
        <Text>{themeManager.primary('╔══════════════════════════════════════╗')}</Text>
        <Text>{themeManager.primary('║')}          {themeManager.accent('🤖 ARIEN AI CLI')}          {themeManager.primary('║')}</Text>
        <Text>{themeManager.primary('║')}     {themeManager.secondary('AI-Powered Terminal Assistant')}     {themeManager.primary('║')}</Text>
        <Text>{themeManager.primary('╚══════════════════════════════════════╝')}</Text>
      </Box>
    );
  };

  const renderLoadingSteps = () => {
    return (
      <Box flexDirection="column" marginBottom={1}>
        {loadingSteps.map((step, index) => {
          const isActive = index === currentStep;
          const isComplete = step.status === 'complete';
          const isError = step.status === 'error';
          const isPending = step.status === 'pending';

          let statusIcon = '';
          let statusColor = (text: string) => themeManager.muted(text);

          if (isComplete) {
            statusIcon = themeManager.getCurrentTheme().symbols.check;
            statusColor = (text: string) => themeManager.success(text);
          } else if (isError) {
            statusIcon = themeManager.getCurrentTheme().symbols.cross;
            statusColor = (text: string) => themeManager.error(text);
          } else if (isActive) {
            statusIcon = '';
            statusColor = (text: string) => themeManager.primary(text);
          } else if (isPending) {
            statusIcon = '○';
            statusColor = (text: string) => themeManager.muted(text);
          }

          return (
            <Box key={step.id} marginLeft={2}>
              <Text>
                {isActive ? (
                  <LoadingIndicator 
                    themeManager={themeManager} 
                    text="" 
                    type="spinner" 
                  />
                ) : (
                  statusColor(statusIcon)
                )}
                {' '}
                {isActive ? statusColor(step.label) : statusColor(step.label)}
              </Text>
            </Box>
          );
        })}
      </Box>
    );
  };

  const renderProgressBar = () => {
    if (!showProgress) return null;

    const completedSteps = loadingSteps.filter(step => step.status === 'complete').length;
    const totalSteps = loadingSteps.length;
    const progressPercent = Math.round((completedSteps / totalSteps) * 100);
    
    const barWidth = 30;
    const filledWidth = Math.round((progressPercent / 100) * barWidth);
    const emptyWidth = barWidth - filledWidth;

    const progressBar = 
      themeManager.success('█'.repeat(filledWidth)) + 
      themeManager.muted('░'.repeat(emptyWidth));

    return (
      <Box flexDirection="column" marginBottom={1}>
        <Box marginLeft={2}>
          <Text>{themeManager.secondary('Progress: ')}</Text>
          <Text>[{progressBar}]</Text>
          <Text> {themeManager.accent(`${progressPercent}%`)}</Text>
        </Box>
      </Box>
    );
  };

  const renderStatus = () => {
    if (!showStatus) return null;

    return (
      <Box marginLeft={2} marginBottom={1}>
        <Text>{themeManager.muted('Status: ')}</Text>
        <Text>{themeManager.info(status)}</Text>
      </Box>
    );
  };

  const renderTips = () => {
    const tips = [
      'Tip: Use Ctrl+H to view help and keyboard shortcuts',
      'Tip: Multiple AI providers are supported (OpenAI, Google, Anthropic, DeepSeek)',
      'Tip: Function calling enables powerful tool integration',
      'Tip: Use Ctrl+L to clear chat history',
      'Tip: Themes can be changed anytime in settings',
    ];

    const randomTip = tips[Math.floor(Math.random() * tips.length)];

    return (
      <Box marginTop={2} marginLeft={2}>
        <Text>{themeManager.muted(randomTip)}</Text>
      </Box>
    );
  };

  return (
    <Box flexDirection="column" padding={1}>
      {renderBranding()}
      
      <Box flexDirection="column" borderStyle="round" borderColor="blue" padding={1}>
        {renderStatus()}
        {renderProgressBar()}
        {renderLoadingSteps()}
        {renderTips()}
      </Box>
      
      <Box marginTop={1} justifyContent="center">
        <Text>{themeManager.muted('Please wait while we prepare your AI assistant...')}</Text>
      </Box>
    </Box>
  );
};
