/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { Header } from '../ui/Header.js';
import { RadioButtons, RadioOption } from '../ui/RadioButtons.js';

interface ThemeSelectorProps {
  themeManager: ThemeManager;
  onThemeSelected: (themeName: string) => void;
  onSkip: () => void;
}

export const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  themeManager,
  onThemeSelected,
  onSkip,
}) => {
  const [selectedTheme, setSelectedTheme] = useState<string>('');
  const [previewMode, setPreviewMode] = useState(false);
  const [livePreview, setLivePreview] = useState(true);
  const [originalTheme] = useState(themeManager.getCurrentTheme().name);

  const themes = themeManager.getAvailableThemes();

  // Convert themes to RadioOptions
  const themeOptions: RadioOption[] = themes.map(themeItem => ({
    value: themeItem.name,
    label: themeItem.theme.name,
    description: themeItem.theme.description,
  }));

  // Live preview effect - automatically preview themes as user navigates
  useEffect(() => {
    if (livePreview && !previewMode && selectedTheme) {
      themeManager.setTheme(selectedTheme);
    }
  }, [selectedTheme, livePreview, previewMode, themeManager]);

  // Handle additional keyboard shortcuts
  useInput((input, key) => {
    if (input === 'p' || input === 'P') {
      setPreviewMode(true);
    } else if (input === 'l' || input === 'L') {
      setLivePreview(!livePreview);
      if (!livePreview && selectedTheme) {
        themeManager.setTheme(selectedTheme);
      } else {
        themeManager.setTheme(originalTheme);
      }
    } else if (input === 's' || input === 'S') {
      // Reset to original theme before skipping
      themeManager.setTheme(originalTheme);
      onSkip();
    } else if (previewMode && (key.escape || input === 'q' || input === 'Q')) {
      setPreviewMode(false);
      if (livePreview && selectedTheme) {
        themeManager.setTheme(selectedTheme);
      } else {
        themeManager.setTheme(originalTheme);
      }
    }
  });

  const handleThemeSelection = (value: string, option: RadioOption) => {
    setSelectedTheme(value);
  };

  const handleThemeSubmit = (value: string, option: RadioOption) => {
    onThemeSelected(value);
  };

  const renderThemeList = () => (
    <Box flexDirection="column">
      <RadioButtons
        themeManager={themeManager}
        options={themeOptions}
        selectedValue={selectedTheme}
        onSelectionChange={handleThemeSelection}
        onSubmit={handleThemeSubmit}
        title="Available Themes:"
        showDescriptions={true}
        showIndicators={true}
        variant="detailed"
        helpText={`Controls: P Detailed Preview | L Toggle Live Preview ${livePreview ? '[ON]' : '[OFF]'} | S Skip`}
      />
    </Box>
  );

  const renderDetailedPreview = () => {
    const currentThemeItem = themes.find(t => t.name === selectedTheme);
    if (!currentThemeItem) return null;
    const currentTheme = currentThemeItem.theme;

    return (
      <Box flexDirection="column">
        <Text>{themeManager.secondary(`🔍 Detailed Preview: ${currentTheme.name}`)}</Text>
        <Text>{themeManager.muted(currentTheme.description)}</Text>
        <Text></Text>

        {/* Simulated Chat Interface Preview */}
        <Box flexDirection="column" marginLeft={1} borderStyle="round" borderColor="gray" padding={1}>
          <Text>{themeManager.primary('🤖 AI Assistant Chat Interface Preview')}</Text>
          <Text></Text>

          {/* User message */}
          <Box marginLeft={2}>
            <Text>{themeManager.accent('You:')} {themeManager.primary('Hello, can you help me with a task?')}</Text>
          </Box>

          {/* AI response */}
          <Box marginLeft={2} marginTop={1}>
            <Text>{themeManager.secondary('Assistant:')} {themeManager.primary('Of course! I\'d be happy to help.')}</Text>
            <Text>{themeManager.success(`${currentTheme.symbols.check} Task completed successfully`)}</Text>
            <Text>{themeManager.info(`${currentTheme.symbols.info} Additional information available`)}</Text>
            <Text>{themeManager.warning(`${currentTheme.symbols.warning} Please review the output`)}</Text>
          </Box>

          {/* Status indicators */}
          <Box marginTop={1} borderStyle="single" borderColor="gray" padding={1}>
            <Text>{themeManager.muted('Status Indicators:')}</Text>
            <Text>{themeManager.success(`${currentTheme.symbols.check} Success`)} {themeManager.error(`${currentTheme.symbols.cross} Error`)} {themeManager.info(`${currentTheme.symbols.info} Info`)} {themeManager.warning(`${currentTheme.symbols.warning} Warning`)}</Text>
            <Text>{themeManager.muted('Loading: ')} {currentTheme.symbols.loading.join(' ')}</Text>
            <Text>{themeManager.highlight(' Highlighted important text ')}</Text>
          </Box>

          {/* Color palette */}
          <Box marginTop={1}>
            <Text>{themeManager.muted('Color Palette:')}</Text>
            <Text>
              {themeManager.primary('Primary')} {themeManager.secondary('Secondary')} {themeManager.accent('Accent')} {themeManager.muted('Muted')}
            </Text>
          </Box>
        </Box>

        <Text></Text>
        <Text>{themeManager.muted('Use ↑↓ to try other themes, Enter to select, Q/Esc to go back')}</Text>
      </Box>
    );
  };

  // Add a mini preview component for live preview mode
  const renderMiniPreview = () => {
    if (!livePreview || previewMode || !selectedTheme) return null;

    const currentThemeItem = themes.find(t => t.name === selectedTheme);
    if (!currentThemeItem) return null;
    const currentTheme = currentThemeItem.theme;

    return (
      <Box flexDirection="column" marginTop={1} borderStyle="single" borderColor="gray" padding={1}>
        <Text>{themeManager.muted(`Preview: ${currentTheme.name}`)}</Text>
        <Box flexDirection="row" gap={1}>
          <Text>{themeManager.primary('Primary')}</Text>
          <Text>{themeManager.secondary('Secondary')}</Text>
          <Text>{themeManager.accent('Accent')}</Text>
          <Text>{themeManager.success(currentTheme.symbols.check)}</Text>
          <Text>{themeManager.error(currentTheme.symbols.cross)}</Text>
        </Box>
      </Box>
    );
  };

  return (
    <Box flexDirection="column" padding={1}>
      <Header
        themeManager={themeManager}
        title="🎨 Enhanced Theme Selection"
        subtitle={`Choose your preferred CLI theme • ${themes.length} themes available`}
      />
      {!previewMode ? (
        <Box flexDirection="column">
          {renderThemeList()}
          {renderMiniPreview()}
        </Box>
      ) : (
        renderDetailedPreview()
      )}
    </Box>
  );
};
