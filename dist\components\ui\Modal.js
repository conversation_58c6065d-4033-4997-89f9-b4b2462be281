import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { Box, Text, useInput } from 'ink';
export const Modal = ({ themeManager, title, children, isOpen, onClose, actions = [], width = 60, height, closable = true, variant = 'default', }) => {
    useInput((input, key) => {
        if (!isOpen)
            return;
        if (key.escape && closable && onClose) {
            onClose();
            return;
        }
        // Handle action keys
        const action = actions.find(a => a.key === input);
        if (action) {
            action.handler();
        }
    });
    if (!isOpen)
        return null;
    const getBorderColor = () => {
        switch (variant) {
            case 'success':
                return 'green';
            case 'warning':
                return 'yellow';
            case 'error':
                return 'red';
            case 'info':
                return 'blue';
            default:
                return 'white';
        }
    };
    const getVariantIcon = () => {
        const theme = themeManager.getCurrentTheme();
        switch (variant) {
            case 'success':
                return themeManager.success(theme.symbols.check);
            case 'warning':
                return themeManager.warning(theme.symbols.warning);
            case 'error':
                return themeManager.error(theme.symbols.cross);
            case 'info':
                return themeManager.info(theme.symbols.info);
            default:
                return '';
        }
    };
    const renderHeader = () => (_jsxs(Box, { justifyContent: "space-between", borderStyle: "single", borderColor: "gray", paddingX: 1, children: [_jsxs(Box, { children: [getVariantIcon() && _jsxs(Text, { children: [getVariantIcon(), " "] }), _jsx(Text, { children: themeManager.primary(title) })] }), closable && onClose && (_jsx(Text, { children: themeManager.muted('[ESC to close]') }))] }));
    const renderContent = () => (_jsx(Box, { padding: 1, flexDirection: "column", minHeight: height, children: children }));
    const renderActions = () => {
        if (actions.length === 0)
            return null;
        return (_jsx(Box, { borderStyle: "single", borderColor: "gray", paddingX: 1, justifyContent: "space-between", children: _jsx(Box, { gap: 2, children: actions.map((action, index) => {
                    const getActionColor = () => {
                        switch (action.variant) {
                            case 'primary':
                                return themeManager.primary;
                            case 'danger':
                                return themeManager.error;
                            case 'secondary':
                            default:
                                return themeManager.secondary;
                        }
                    };
                    return (_jsxs(Text, { children: [themeManager.accent(`[${action.key}]`), " ", getActionColor()(action.label)] }, index));
                }) }) }));
    };
    return (_jsx(Box, { justifyContent: "center", alignItems: "center", height: "100%", width: "100%", children: _jsxs(Box, { flexDirection: "column", borderStyle: "double", borderColor: getBorderColor(), width: width, minHeight: height, paddingX: 2, paddingY: 1, children: [renderHeader(), renderContent(), renderActions()] }) }));
};
export const ConfirmModal = ({ themeManager, title, message, isOpen, onConfirm, onCancel, confirmLabel = 'Confirm', cancelLabel = 'Cancel', variant = 'default', }) => {
    const actions = [
        {
            label: confirmLabel,
            key: 'y',
            handler: onConfirm,
            variant: variant === 'danger' ? 'danger' : 'primary',
        },
        {
            label: cancelLabel,
            key: 'n',
            handler: onCancel,
            variant: 'secondary',
        },
    ];
    return (_jsx(Modal, { themeManager: themeManager, title: title, isOpen: isOpen, onClose: onCancel, actions: actions, variant: variant === 'danger' ? 'error' : 'default', width: 50, children: _jsx(Text, { children: message }) }));
};
export const AlertModal = ({ themeManager, title, message, isOpen, onClose, variant = 'info', }) => {
    const actions = [
        {
            label: 'OK',
            key: 'Enter',
            handler: onClose,
            variant: 'primary',
        },
    ];
    return (_jsx(Modal, { themeManager: themeManager, title: title, isOpen: isOpen, onClose: onClose, actions: actions, variant: variant, width: 50, children: _jsx(Text, { children: message }) }));
};
//# sourceMappingURL=Modal.js.map