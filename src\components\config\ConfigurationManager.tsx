/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';

interface ConfigSection {
  id: string;
  name: string;
  description: string;
  settings: ConfigSetting[];
}

interface ConfigSetting {
  id: string;
  name: string;
  description: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'password';
  value: any;
  defaultValue: any;
  options?: string[];
  required?: boolean;
  validation?: (value: any) => string | null;
}

interface ConfigurationManagerProps {
  themeManager: ThemeManager;
  isOpen: boolean;
  onClose: () => void;
  onSave?: (config: Record<string, any>) => void;
}

export const ConfigurationManager: React.FC<ConfigurationManagerProps> = ({
  themeManager,
  isOpen,
  onClose,
  onSave,
}) => {
  const [selectedSectionIndex, setSelectedSectionIndex] = useState(0);
  const [selectedSettingIndex, setSelectedSettingIndex] = useState(0);
  const [editingValue, setEditingValue] = useState<string>('');
  const [, setIsEditing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [mode, setMode] = useState<'sections' | 'settings' | 'editing'>('sections');

  const [configSections, setConfigSections] = useState<ConfigSection[]>([
    {
      id: 'general',
      name: 'General Settings',
      description: 'Basic application configuration',
      settings: [
        {
          id: 'theme',
          name: 'Theme',
          description: 'Application color theme',
          type: 'select',
          value: 'default',
          defaultValue: 'default',
          options: ['default', 'dark', 'light', 'neon', 'minimal', 'retro'],
          required: true,
        },
        {
          id: 'autoSave',
          name: 'Auto Save',
          description: 'Automatically save conversations',
          type: 'boolean',
          value: true,
          defaultValue: true,
        },
        {
          id: 'maxMessages',
          name: 'Max Messages',
          description: 'Maximum messages to keep in memory',
          type: 'number',
          value: 100,
          defaultValue: 100,
          validation: (value) => {
            const num = Number(value);
            if (isNaN(num) || num < 1 || num > 1000) {
              return 'Must be a number between 1 and 1000';
            }
            return null;
          },
        },
      ],
    },
    {
      id: 'providers',
      name: 'AI Providers',
      description: 'Configure AI service providers',
      settings: [
        {
          id: 'defaultProvider',
          name: 'Default Provider',
          description: 'Default AI provider to use',
          type: 'select',
          value: 'openai',
          defaultValue: 'openai',
          options: ['openai', 'anthropic', 'google', 'deepseek'],
          required: true,
        },
        {
          id: 'openaiApiKey',
          name: 'OpenAI API Key',
          description: 'API key for OpenAI services',
          type: 'password',
          value: '',
          defaultValue: '',
        },
        {
          id: 'anthropicApiKey',
          name: 'Anthropic API Key',
          description: 'API key for Anthropic services',
          type: 'password',
          value: '',
          defaultValue: '',
        },
        {
          id: 'googleApiKey',
          name: 'Google API Key',
          description: 'API key for Google AI services',
          type: 'password',
          value: '',
          defaultValue: '',
        },
      ],
    },
    {
      id: 'interface',
      name: 'Interface',
      description: 'User interface preferences',
      settings: [
        {
          id: 'showTimestamps',
          name: 'Show Timestamps',
          description: 'Display message timestamps',
          type: 'boolean',
          value: true,
          defaultValue: true,
        },
        {
          id: 'messageFormat',
          name: 'Message Format',
          description: 'How to display messages',
          type: 'select',
          value: 'markdown',
          defaultValue: 'markdown',
          options: ['markdown', 'plain', 'code'],
        },
        {
          id: 'terminalWidth',
          name: 'Terminal Width',
          description: 'Maximum width for text display',
          type: 'number',
          value: 80,
          defaultValue: 80,
          validation: (value) => {
            const num = Number(value);
            if (isNaN(num) || num < 40 || num > 200) {
              return 'Must be a number between 40 and 200';
            }
            return null;
          },
        },
      ],
    },
  ]);

  useInput((input, key) => {
    if (!isOpen) return;

    if (key.escape) {
      if (mode === 'editing') {
        setMode('settings');
        setIsEditing(false);
        setEditingValue('');
        return;
      }
      if (mode === 'settings') {
        setMode('sections');
        return;
      }
      if (hasChanges) {
        // Could show confirmation dialog here
      }
      onClose();
      return;
    }

    if (mode === 'sections') {
      if (key.upArrow && selectedSectionIndex > 0) {
        setSelectedSectionIndex(selectedSectionIndex - 1);
        return;
      }
      if (key.downArrow && selectedSectionIndex < configSections.length - 1) {
        setSelectedSectionIndex(selectedSectionIndex + 1);
        return;
      }
      if (key.return) {
        setMode('settings');
        setSelectedSettingIndex(0);
        return;
      }
    }

    if (mode === 'settings') {
      const currentSection = configSections[selectedSectionIndex];
      
      if (key.upArrow && selectedSettingIndex > 0) {
        setSelectedSettingIndex(selectedSettingIndex - 1);
        return;
      }
      if (key.downArrow && selectedSettingIndex < currentSection.settings.length - 1) {
        setSelectedSettingIndex(selectedSettingIndex + 1);
        return;
      }
      if (key.return) {
        const setting = currentSection.settings[selectedSettingIndex];
        if (setting.type === 'boolean') {
          updateSettingValue(setting.id, !setting.value);
        } else {
          setMode('editing');
          setIsEditing(true);
          setEditingValue(setting.value?.toString() || '');
        }
        return;
      }
    }

    if (mode === 'editing') {
      if (key.return) {
        const setting = configSections[selectedSectionIndex].settings[selectedSettingIndex];
        const validation = setting.validation?.(editingValue);
        if (!validation) {
          let value: any = editingValue;
          if (setting.type === 'number') {
            value = Number(editingValue);
          } else if (setting.type === 'boolean') {
            value = editingValue.toLowerCase() === 'true';
          }
          updateSettingValue(setting.id, value);
          setMode('settings');
          setIsEditing(false);
          setEditingValue('');
        }
        return;
      }

      if (key.backspace || key.delete) {
        setEditingValue(prev => prev.slice(0, -1));
        return;
      }

      if (input && input.length === 1 && !key.ctrl && !key.meta) {
        setEditingValue(prev => prev + input);
      }
    }

    // Global shortcuts
    if (key.ctrl && input === 's') {
      handleSave();
      return;
    }
  });

  const updateSettingValue = (settingId: string, value: any) => {
    setConfigSections(prev => prev.map(section => ({
      ...section,
      settings: section.settings.map(setting =>
        setting.id === settingId ? { ...setting, value } : setting
      ),
    })));
    setHasChanges(true);
  };

  const handleSave = () => {
    const config: Record<string, any> = {};
    configSections.forEach(section => {
      section.settings.forEach(setting => {
        config[setting.id] = setting.value;
      });
    });
    onSave?.(config);
    setHasChanges(false);
  };



  if (!isOpen) return null;

  const renderSectionsList = () => (
    <Box flexDirection="column">
      <Text>{themeManager.primary('Configuration Sections:')}</Text>
      <Text></Text>
      {configSections.map((section, index) => (
        <Box key={section.id} marginLeft={2}>
          <Text>
            {selectedSectionIndex === index ? themeManager.highlight(' ▶ ') : '   '}
            {themeManager.primary(section.name)}
          </Text>
          <Text> - {themeManager.muted(section.description)}</Text>
        </Box>
      ))}
    </Box>
  );

  const renderSettingsList = () => {
    const currentSection = configSections[selectedSectionIndex];
    
    return (
      <Box flexDirection="column">
        <Text>{themeManager.primary(`${currentSection.name} Settings:`)}</Text>
        <Text></Text>
        {currentSection.settings.map((setting, index) => (
          <Box key={setting.id} marginLeft={2} marginBottom={1}>
            <Box flexDirection="column">
              <Box>
                <Text>
                  {selectedSettingIndex === index ? themeManager.highlight(' ▶ ') : '   '}
                  {themeManager.primary(setting.name)}
                  {setting.required && <Text>{themeManager.error(' *')}</Text>}
                </Text>
              </Box>
              <Box marginLeft={3}>
                <Text>{themeManager.muted(setting.description)}</Text>
              </Box>
              <Box marginLeft={3}>
                <Text>{themeManager.secondary('Value: ')}</Text>
                {setting.type === 'password' ? (
                  <Text>{themeManager.accent('*'.repeat(setting.value?.length || 0))}</Text>
                ) : (
                  <Text>{themeManager.accent(setting.value?.toString() || 'Not set')}</Text>
                )}
              </Box>
            </Box>
          </Box>
        ))}
      </Box>
    );
  };

  const renderEditingInterface = () => {
    const setting = configSections[selectedSectionIndex].settings[selectedSettingIndex];
    const validation = setting.validation?.(editingValue);

    return (
      <Box flexDirection="column">
        <Text>{themeManager.primary(`Editing: ${setting.name}`)}</Text>
        <Text></Text>
        <Box marginLeft={2}>
          <Text>{themeManager.secondary('Description: ')}</Text>
          <Text>{themeManager.muted(setting.description)}</Text>
        </Box>
        <Text></Text>
        <Box marginLeft={2}>
          <Text>{themeManager.secondary('Current Value: ')}</Text>
          <Text>{themeManager.primary(editingValue || '...')}</Text>
        </Box>
        
        {setting.options && (
          <Box marginLeft={2} marginTop={1}>
            <Text>{themeManager.secondary('Options: ')}</Text>
            <Text>{themeManager.muted(setting.options.join(', '))}</Text>
          </Box>
        )}
        
        {validation && (
          <Box marginLeft={2} marginTop={1}>
            <Text>{themeManager.error(`Error: ${validation}`)}</Text>
          </Box>
        )}
      </Box>
    );
  };

  const renderControls = () => (
    <Box marginTop={1} borderStyle="single" borderColor="gray" padding={1}>
      <Box flexDirection="column">
        <Text>{themeManager.muted('Controls:')}</Text>
        {mode === 'sections' && (
          <Text>{themeManager.muted('  ↑↓ Navigate • Enter Select • Esc Close')}</Text>
        )}
        {mode === 'settings' && (
          <Text>{themeManager.muted('  ↑↓ Navigate • Enter Edit • Esc Back • Ctrl+S Save')}</Text>
        )}
        {mode === 'editing' && (
          <Text>{themeManager.muted('  Type value • Enter Save • Esc Cancel')}</Text>
        )}
        {hasChanges && (
          <Text>{themeManager.warning('  Unsaved changes!')}</Text>
        )}
      </Box>
    </Box>
  );

  return (
    <Box flexDirection="column" padding={1}>
      <Box borderStyle="round" borderColor="blue" padding={1}>
        <Box flexDirection="column" width="100%">
          <Text>{themeManager.primary('⚙️  Configuration Manager')}</Text>
          <Text></Text>
          
          {mode === 'sections' && renderSectionsList()}
          {mode === 'settings' && renderSettingsList()}
          {mode === 'editing' && renderEditingInterface()}
          
          {renderControls()}
        </Box>
      </Box>
    </Box>
  );
};
